[PS] C:\TEMP>.\Exchange-Mailbox-Security-Audit.ps1 -DomainFilter "albaraka.com"
[INFO] PowerShell Version: 5.1.14393.8244 - Compatible
=================================================================================
Exchange Server Mailbox Security Audit Script
=================================================================================
Script Version: 1.6.5 (August 25, 2025)
Created By: E.Z. Consultancy
PowerShell Version: 5.1.14393.8244
PowerShell Edition: Desktop
Execution Host: BARAKAEXCH02
Execution User: ad21bar
Execution Time: 2025-09-10 17:03:12
Domain Filter: albaraka.com
Max Sample Size: 500 mailboxes
Output Location: .\Exchange-Mailbox-Security-Results.json
=================================================================================
Audit ID: 92c2850c-9fe6-46a3-92dd-e599f06a59e0
=================================================================================

Step 1/7: Collecting Exchange environment metadata...
  Domain albaraka.com : 0 mailboxes
  [WARNING] Server-side counting failed, using client-side counting (may be slower)
  Domain filter applied: 149 mailboxes match filter
[SUCCESS] Environment metadata collected successfully
  Metadata sections: 17
Step 2/7: Performing administrator discovery...
  Performing administrator discovery across all domains...
  Discovered 220 role assignments across 1 domains
[SUCCESS] Administrator discovery completed
Performing pre-flight validation...
[SUCCESS] Exchange PowerShell module is available
[SUCCESS] Exchange connectivity verified
Step 3/7: Assessing mailbox impersonation rights...
[SUCCESS] Mailbox impersonation rights assessed
Step 4/7: Assessing mailbox full access permissions...
  Applying domain filter for: albaraka.com
  Found 0 mailboxes matching domain filter
  No mailboxes found matching the criteria
  [INFO] Cross-domain analysis skipped (insufficient data or administrator discovery failed)
[SUCCESS] Full access permissions assessed
Step 5/7: Assessing mailbox audit logging configuration...
WARNING: The object baraka.com/Resigned Staff/AIB-Resigned Staff/Zaheer A. Naseer has been corrupted or isn't
compatible with Microsoft support requirements, and it's in an inconsistent state. The following validation errors
happened:
WARNING: Could not convert property WindowsEmailAddress to type SmtpAddress. Error while converting string
'ad12bar@@albaraka.bh' to result type Microsoft.Exchange.Data.SmtpAddress: The email address "ad12bar@@albaraka.bh"
isn't correct. Please use this format: user name, the @ sign, followed by the domain name. For example,
tonysmith@contoso.<NAME_EMAIL>.
WARNING: The object baraka.com/Resigned Staff/Vendors/Abdul Vahab Abdulrasak has been corrupted or isn't compatible
with Microsoft support requirements, and it's in an inconsistent state. The following validation errors happened:
WARNING: Could not convert property WindowsEmailAddress to type SmtpAddress. Error while converting string
'<EMAIL> ' to result type Microsoft.Exchange.Data.SmtpAddress: The email address
"<EMAIL> " isn't correct. Please use this format: user name, the @ sign, followed by the domain name.
 For example, tonysmith@contoso.<NAME_EMAIL>.
WARNING: By default, only the first 1000 items are returned. Use the ResultSize parameter to specify the number of
items returned. To return all items, specify "-ResultSize Unlimited". Be aware that, depending on the actual number of
items, returning all items can take a long time and consume a large amount of memory. Also, we don't recommend storing
the results in a variable. Instead, pipe the results to another task or script to perform batch changes.
[SUCCESS] Audit logging configuration assessed
Step 6/7: Assessing Send-As permissions...
[SUCCESS] Send-As permissions assessed
Step 7/7: Assessing Send-On-Behalf permissions...
[SUCCESS] Send-On-Behalf permissions assessed

Generating compliance summary...

=== AUDIT DATA COLLECTION SUMMARY ===
Total audit sections collected: 8
  - MBX_4_1_SendAsPermissions : SUCCESS
  - MBX_1_1_ImpersonationRights : SUCCESS
  - AuditMetadata : SUCCESS
  - MBX_2_1_FullAccessPermissions : SUCCESS
  - MBX_3_1_AuditLogging : SUCCESS
  - ComplianceSummary : SUCCESS
  - AdministratorDiscovery : SUCCESS
  - MBX_5_1_SendOnBehalfPermissions : SUCCESS
=======================================

Generating JSON output...
  Converting 8 audit sections to JSON...
[ERROR] Failed to save JSON output: The type 'System.Collections.Hashtable' is not supported for serialization or deserialization of a dictionary. Keys must be strings.
Error Details: InvalidOperationException
Attempting to regenerate JSON output...
[ERROR] JSON regeneration failed: The type 'System.Collections.Hashtable' is not supported for serialization or deserialization of a dictionary. Keys must be strings.
Attempting to save to alternative location...
[SUCCESS] JSON output saved to alternative location: .\Exchange-Mailbox-Security-Results-20250910-170355.json
[PS] C:\TEMP>