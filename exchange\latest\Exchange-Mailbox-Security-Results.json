﻿{
    "MBX_4_1_SendAsPermissions":  {
                                      "ControlName":  "Send-As Permissions",
                                      "CVSSScore":  7.6,
                                      "AssessmentDate":  "2025-09-11 00:39:33",
                                      "SampleSize":  5,
                                      "CurrentValue":  "5 Send-As permissions found across 5 mailboxes",
                                      "SendAsPermissions":  [
                                                                {
                                                                    "ExtendedRights":  "Send-As",
                                                                    "MailboxIdentity":  "<EMAIL>",
                                                                    "AccessControlType":  "Allow",
                                                                    "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                    "IsInherited":  false,
                                                                    "MailboxDisplayName":  "Test User 1",
                                                                    "User":  "<EMAIL>"
                                                                },
                                                                {
                                                                    "ExtendedRights":  "Send-As",
                                                                    "MailboxIdentity":  "<EMAIL>",
                                                                    "AccessControlType":  "Allow",
                                                                    "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                    "IsInherited":  false,
                                                                    "MailboxDisplayName":  "Test User 2",
                                                                    "User":  "<EMAIL>"
                                                                },
                                                                {
                                                                    "ExtendedRights":  "Send-As",
                                                                    "MailboxIdentity":  "<EMAIL>",
                                                                    "AccessControlType":  "Allow",
                                                                    "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                    "IsInherited":  false,
                                                                    "MailboxDisplayName":  "Test User 3",
                                                                    "User":  "<EMAIL>"
                                                                },
                                                                {
                                                                    "ExtendedRights":  "Send-As",
                                                                    "MailboxIdentity":  "<EMAIL>",
                                                                    "AccessControlType":  "Allow",
                                                                    "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                    "IsInherited":  false,
                                                                    "MailboxDisplayName":  "Test User 4",
                                                                    "User":  "<EMAIL>"
                                                                },
                                                                {
                                                                    "ExtendedRights":  "Send-As",
                                                                    "MailboxIdentity":  "<EMAIL>",
                                                                    "AccessControlType":  "Allow",
                                                                    "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                    "IsInherited":  false,
                                                                    "MailboxDisplayName":  "Test User 5",
                                                                    "User":  "<EMAIL>"
                                                                }
                                                            ],
                                      "TotalSendAsPermissions":  5,
                                      "UniqueUsersWithSendAs":  null,
                                      "Recommendation":  "Regularly review Send-As permissions and ensure they are justified by business requirements",
                                      "BaselineValue":  "Minimal Send-As permissions with business justification",
                                      "Finding":  "High number of Send-As permissions - review required",
                                      "ComplianceScore":  25,
                                      "RiskLevel":  "High",
                                      "ComplianceStatus":  "Review Required",
                                      "ControlID":  "MBX-4.1"
                                  },
    "MBX_5_1_SendOnBehalfPermissions":  {
                                            "ControlName":  "Send-On-Behalf Permissions",
                                            "CVSSScore":  6.2,
                                            "AssessmentDate":  "2025-09-11 00:39:33",
                                            "SampleSize":  5,
                                            "TotalSendOnBehalfPermissions":  1,
                                            "CurrentValue":  "1 Send-On-Behalf permissions found across 5 mailboxes",
                                            "UniqueUsersWithSendOnBehalf":  null,
                                            "SendOnBehalfPermissions":  {
                                                                            "MailboxDisplayName":  "Test User 1",
                                                                            "MailboxIdentity":  "<EMAIL>",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "PermissionType":  "Send-On-Behalf",
                                                                            "DelegateUser":  "<EMAIL>"
                                                                        },
                                            "Recommendation":  "Regularly review Send-On-Behalf permissions and ensure they align with current business needs",
                                            "BaselineValue":  "Minimal Send-On-Behalf permissions with business justification",
                                            "Finding":  "High number of Send-On-Behalf permissions - review required",
                                            "ComplianceScore":  25,
                                            "RiskLevel":  "Medium",
                                            "ComplianceStatus":  "Review Required",
                                            "ControlID":  "MBX-5.1"
                                        },
    "AuditMetadata":  {
                          "PowerShellEdition":  "Desktop",
                          "ScriptAuthor":  "E.Z. Consultancy",
                          "ScriptReleaseDate":  "August 25, 2025",
                          "PowerShellVersion":  "5.1.26100.6584",
                          "ScriptVersion":  "1.6.5",
                          "FilteredDomains":  "albaraka.com",
                          "AuditUser":  "ealza",
                          "AssessmentScope":  "5 Critical Mailbox Security Controls",
                          "MaxMailboxSample":  10,
                          "OrganizationName":  "Test Organization",
                          "DomainFilterScope":  "Domain-specific audit for: albaraka.com",
                          "DomainFilterEnabled":  true,
                          "AuditID":  "1a120a69-90f6-456c-a2a7-fefb2ba2d832",
                          "ExchangeServers":  null,
                          "ComputerName":  "PRD",
                          "TotalMailboxes":  "Unknown",
                          "AuditStartTime":  "2025-09-11 00:39:33"
                      },
    "MBX_1_1_ImpersonationRights":  {
                                        "ControlName":  "Mailbox Impersonation Rights",
                                        "ImpersonationUsers":  {
                                                                   "RoleAssigneeType":  "User",
                                                                   "WhenCreated":  "2025-09-11 00:39:33",
                                                                   "IsValid":  true,
                                                                   "AssignmentMethod":  "Direct",
                                                                   "RoleAssignee":  "<EMAIL>",
                                                                   "RoleAssigneeName":  "Test Administrator"
                                                               },
                                        "AssessmentDate":  "2025-09-11 00:39:33",
                                        "TotalImpersonationAssignments":  null,
                                        "CurrentValue":  " accounts with ApplicationImpersonation rights",
                                        "CVSSScore":  9.3,
                                        "Recommendation":  "Regularly review ApplicationImpersonation role assignments and remove unnecessary permissions",
                                        "BaselineValue":  "Minimal impersonation rights with regular review",
                                        "Finding":  "Limited impersonation rights - review assignments",
                                        "ComplianceScore":  75,
                                        "RiskLevel":  "Critical",
                                        "ComplianceStatus":  "Compliant",
                                        "ControlID":  "MBX-1.1"
                                    },
    "MBX_2_1_FullAccessPermissions":  {
                                          "ControlName":  "Mailbox Full Access Permissions",
                                          "CVSSScore":  8.1,
                                          "AssessmentDate":  "2025-09-11 00:39:33",
                                          "SampleSize":  5,
                                          "TotalFullAccessPermissions":  5,
                                          "CurrentValue":  "5 Full Access permissions found across 5 mailboxes",
                                          "CrossDomainAnalysis":  {
                                                                      "PermissionSummary":  {

                                                                                            },
                                                                      "CrossDomainRelationships":  [
                                                                                                       {
                                                                                                           "AccessRights":  "FullAccess",
                                                                                                           "IsCrossDomain":  false,
                                                                                                           "AdminDomain":  "albaraka.com",
                                                                                                           "MailboxDisplayName":  "Test User 1",
                                                                                                           "MailboxDomain":  "albaraka.com",
                                                                                                           "AdminClassification":  "Business-User-Delegate",
                                                                                                           "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                                           "IsInherited":  false,
                                                                                                           "RiskScore":  3,
                                                                                                           "RiskLevel":  "Medium",
                                                                                                           "MailboxIdentity":  "<EMAIL>",
                                                                                                           "PermissionType":  "Direct",
                                                                                                           "AdminUser":  "<EMAIL>"
                                                                                                       },
                                                                                                       {
                                                                                                           "AccessRights":  "FullAccess",
                                                                                                           "IsCrossDomain":  false,
                                                                                                           "AdminDomain":  "albaraka.com",
                                                                                                           "MailboxDisplayName":  "Test User 2",
                                                                                                           "MailboxDomain":  "albaraka.com",
                                                                                                           "AdminClassification":  "Business-User-Delegate",
                                                                                                           "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                                           "IsInherited":  false,
                                                                                                           "RiskScore":  3,
                                                                                                           "RiskLevel":  "Medium",
                                                                                                           "MailboxIdentity":  "<EMAIL>",
                                                                                                           "PermissionType":  "Direct",
                                                                                                           "AdminUser":  "<EMAIL>"
                                                                                                       },
                                                                                                       {
                                                                                                           "AccessRights":  "FullAccess",
                                                                                                           "IsCrossDomain":  false,
                                                                                                           "AdminDomain":  "albaraka.com",
                                                                                                           "MailboxDisplayName":  "Test User 3",
                                                                                                           "MailboxDomain":  "albaraka.com",
                                                                                                           "AdminClassification":  "Business-User-Delegate",
                                                                                                           "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                                           "IsInherited":  false,
                                                                                                           "RiskScore":  3,
                                                                                                           "RiskLevel":  "Medium",
                                                                                                           "MailboxIdentity":  "<EMAIL>",
                                                                                                           "PermissionType":  "Direct",
                                                                                                           "AdminUser":  "<EMAIL>"
                                                                                                       },
                                                                                                       {
                                                                                                           "AccessRights":  "FullAccess",
                                                                                                           "IsCrossDomain":  false,
                                                                                                           "AdminDomain":  "albaraka.com",
                                                                                                           "MailboxDisplayName":  "Test User 4",
                                                                                                           "MailboxDomain":  "albaraka.com",
                                                                                                           "AdminClassification":  "Business-User-Delegate",
                                                                                                           "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                                           "IsInherited":  false,
                                                                                                           "RiskScore":  3,
                                                                                                           "RiskLevel":  "Medium",
                                                                                                           "MailboxIdentity":  "<EMAIL>",
                                                                                                           "PermissionType":  "Direct",
                                                                                                           "AdminUser":  "<EMAIL>"
                                                                                                       },
                                                                                                       {
                                                                                                           "AccessRights":  "FullAccess",
                                                                                                           "IsCrossDomain":  false,
                                                                                                           "AdminDomain":  "albaraka.com",
                                                                                                           "MailboxDisplayName":  "Test User 5",
                                                                                                           "MailboxDomain":  "albaraka.com",
                                                                                                           "AdminClassification":  "Business-User-Delegate",
                                                                                                           "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                                                           "IsInherited":  false,
                                                                                                           "RiskScore":  3,
                                                                                                           "RiskLevel":  "Medium",
                                                                                                           "MailboxIdentity":  "<EMAIL>",
                                                                                                           "PermissionType":  "Direct",
                                                                                                           "AdminUser":  "<EMAIL>"
                                                                                                       }
                                                                                                   ],
                                                                      "RiskAssessment":  {
                                                                                             "TotalPermissionRelationships":  5,
                                                                                             "CrossDomainRelationships":  0,
                                                                                             "HighRiskRelationships":  0,
                                                                                             "CrossDomainPercentage":  0,
                                                                                             "RiskDistribution":  {
                                                                                                                      "Low":  0,
                                                                                                                      "Medium":  5,
                                                                                                                      "High":  0
                                                                                                                  }
                                                                                         },
                                                                      "ComplianceGaps":  {

                                                                                         }
                                                                  },
                                          "FullAccessPermissions":  [
                                                                        {
                                                                            "Deny":  false,
                                                                            "User":  "<EMAIL>",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Test User 1",
                                                                            "MailboxIdentity":  "<EMAIL>"
                                                                        },
                                                                        {
                                                                            "Deny":  false,
                                                                            "User":  "<EMAIL>",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Test User 2",
                                                                            "MailboxIdentity":  "<EMAIL>"
                                                                        },
                                                                        {
                                                                            "Deny":  false,
                                                                            "User":  "<EMAIL>",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Test User 3",
                                                                            "MailboxIdentity":  "<EMAIL>"
                                                                        },
                                                                        {
                                                                            "Deny":  false,
                                                                            "User":  "<EMAIL>",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Test User 4",
                                                                            "MailboxIdentity":  "<EMAIL>"
                                                                        },
                                                                        {
                                                                            "Deny":  false,
                                                                            "User":  "<EMAIL>",
                                                                            "AccessRights":  "FullAccess",
                                                                            "MailboxPrimarySmtpAddress":  "<EMAIL>",
                                                                            "IsInherited":  false,
                                                                            "MailboxDisplayName":  "Test User 5",
                                                                            "MailboxIdentity":  "<EMAIL>"
                                                                        }
                                                                    ],
                                          "UniqueUsersWithFullAccess":  null,
                                          "Recommendation":  "Regularly review Full Access permissions and remove unnecessary delegations. Consider using Send-As or Send-On-Behalf instead.",
                                          "BaselineValue":  "Minimal Full Access delegations with business justification",
                                          "Finding":  "High number of Full Access permissions - review required",
                                          "ComplianceScore":  25,
                                          "RiskLevel":  "High",
                                          "ComplianceStatus":  "Review Required",
                                          "ControlID":  "MBX-2.1"
                                      },
    "MBX_3_1_AuditLogging":  {
                                 "ControlName":  "Mailbox Audit Logging Configuration",
                                 "CVSSScore":  6.8,
                                 "MailboxAuditBypassUsers":  "",
                                 "SampleSize":  5,
                                 "AssessmentDate":  "2025-09-11 00:39:33",
                                 "AuditEnabledMailboxes":  2,
                                 "CurrentValue":  "Admin audit: True, Mailbox audit enabled: 2 of 5 sampled",
                                 "AdminAuditLogEnabled":  true,
                                 "SampleMailboxAuditSettings":  [
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Test User 1",
                                                                        "Identity":  "<EMAIL>"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  true,
                                                                        "DisplayName":  "Test User 2",
                                                                        "Identity":  "<EMAIL>"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Test User 3",
                                                                        "Identity":  "<EMAIL>"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  true,
                                                                        "DisplayName":  "Test User 4",
                                                                        "Identity":  "<EMAIL>"
                                                                    },
                                                                    {
                                                                        "AuditLogAgeLimit":  "90.00:00:00",
                                                                        "AuditEnabled":  false,
                                                                        "DisplayName":  "Test User 5",
                                                                        "Identity":  "<EMAIL>"
                                                                    }
                                                                ],
                                 "AuditBypassCount":  null,
                                 "Recommendation":  "Ensure mailbox audit is enabled for all mailboxes",
                                 "BaselineValue":  "Admin and mailbox audit logging enabled organization-wide",
                                 "Finding":  "Admin audit logging enabled",
                                 "ComplianceScore":  75,
                                 "RiskLevel":  "Medium",
                                 "ComplianceStatus":  "Review Required",
                                 "ControlID":  "MBX-3.1"
                             },
    "ComplianceSummary":  {
                              "SuccessfulAssessments":  5,
                              "PowerShellVersion":  "5.1.26100.6584",
                              "FrameworkVersion":  "Exchange Mailbox Security Controls v1.0",
                              "AssessmentType":  "Exchange Server Mailbox Security Controls Audit",
                              "AuditDurationSeconds":  0.19,
                              "AuditExecutedBy":  "ealza",
                              "AuditID":  "1a120a69-90f6-456c-a2a7-fefb2ba2d832",
                              "ComputerName":  "PRD",
                              "FailedAssessments":  0,
                              "AuditEndTime":  "2025-09-11 00:39:33",
                              "TotalControlsAssessed":  5,
                              "AuditType":  "Read-Only Assessment - Production Safe",
                              "AuditStartTime":  "2025-09-11 00:39:33"
                          },
    "AdministratorDiscovery":  {
                                   "CrossDomainRelationships":  {

                                                                },
                                   "AdminDomainMap":  {
                                                          "albaraka.com":  [
                                                                               {
                                                                                   "Role":  "Organization Management",
                                                                                   "AdminClassification":  "Exchange-Administrator",
                                                                                   "AssigneeType":  "User",
                                                                                   "AssigneeDomain":  "albaraka.com",
                                                                                   "RoleType":  "Management",
                                                                                   "AssignmentMethod":  "Direct",
                                                                                   "RoleAssignee":  "<EMAIL>",
                                                                                   "Scope":  {
                                                                                                 "AffectedDomains":  {

                                                                                                                     },
                                                                                                 "Type":  "Unknown",
                                                                                                 "IsOrganizationWide":  false
                                                                                             },
                                                                                   "IsEnabled":  true
                                                                               },
                                                                               {
                                                                                   "Role":  "Recipient Management",
                                                                                   "AdminClassification":  "Mailbox-Administrator",
                                                                                   "AssigneeType":  "User",
                                                                                   "AssigneeDomain":  "albaraka.com",
                                                                                   "RoleType":  "Management",
                                                                                   "AssignmentMethod":  "Direct",
                                                                                   "RoleAssignee":  "<EMAIL>",
                                                                                   "Scope":  {
                                                                                                 "AffectedDomains":  {

                                                                                                                     },
                                                                                                 "Type":  "Unknown",
                                                                                                 "IsOrganizationWide":  false
                                                                                             },
                                                                                   "IsEnabled":  true
                                                                               }
                                                                           ]
                                                      },
                                   "RoleAssignments":  [
                                                           {
                                                               "Role":  "Organization Management",
                                                               "AdminClassification":  "Exchange-Administrator",
                                                               "AssigneeType":  "User",
                                                               "AssigneeDomain":  "albaraka.com",
                                                               "RoleType":  "Management",
                                                               "AssignmentMethod":  "Direct",
                                                               "RoleAssignee":  "<EMAIL>",
                                                               "Scope":  {
                                                                             "AffectedDomains":  {

                                                                                                 },
                                                                             "Type":  "Unknown",
                                                                             "IsOrganizationWide":  false
                                                                         },
                                                               "IsEnabled":  true
                                                           },
                                                           {
                                                               "Role":  "Recipient Management",
                                                               "AdminClassification":  "Mailbox-Administrator",
                                                               "AssigneeType":  "User",
                                                               "AssigneeDomain":  "albaraka.com",
                                                               "RoleType":  "Management",
                                                               "AssignmentMethod":  "Direct",
                                                               "RoleAssignee":  "<EMAIL>",
                                                               "Scope":  {
                                                                             "AffectedDomains":  {

                                                                                                 },
                                                                             "Type":  "Unknown",
                                                                             "IsOrganizationWide":  false
                                                                         },
                                                               "IsEnabled":  true
                                                           }
                                                       ],
                                   "AdminClassification":  {
                                                               "<EMAIL>":  "Mailbox-Administrator",
                                                               "<EMAIL>":  "Exchange-Administrator"
                                                           }
                               }
}
